<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Models\Swap;
use App\Models\WebhookCall;
use App\Services\NowPaymentsService;
use App\Services\ZHCashService;
use App\Services\ZHNodeService;
use App\Services\LoggingService;
use App\Models\SwapLog;

class WebhookController extends Controller
{
    private NowPaymentsService $nowPayments;
    private ZHCashService $zhcash;
    private ZHNodeService $zhnode;

    public function __construct(NowPaymentsService $nowPayments, ZHCashService $zhcash, ZHNodeService $zhnode)
    {
        $this->nowPayments = $nowPayments;
        $this->zhcash = $zhcash;
        $this->zhnode = $zhnode;
    }

    /**
     * Handle NowPayments webhook
     */
    public function nowPayments(Request $request)
    {
        $startTime = microtime(true);
        
        try {
            // Get raw payload and headers
            $payload = $request->getContent();
            $headers = $request->headers->all();
            $signature = $request->header('x-nowpayments-sig');

            // Validate signature
            $signatureValid = false;
            if ($signature) {
                $signatureValid = $this->nowPayments->validateWebhookSignature($payload, $signature);
            }

            // Parse payload
            $data = json_decode($payload, true);
            if (!$data) {
                Log::error('Invalid webhook payload', [
                    'payload' => $payload,
                    'headers' => $headers
                ]);
                return response('Invalid payload', 400);
            }

            // Record webhook call
            $webhookCall = WebhookCall::record(
                WebhookCall::SOURCE_NOWPAYMENTS,
                $this->getEventType($data),
                $data,
                $headers,
                $signature,
                $signatureValid
            );

            // Log webhook received
            LoggingService::logWebhookReceived(
                WebhookCall::SOURCE_NOWPAYMENTS,
                $this->getEventType($data),
                $data,
                $data['order_id'] ?? null
            );

            // Only process webhooks with valid signatures in production
            if (!$signatureValid && !config('app.debug')) {
                $webhookCall->markFailed('Invalid signature', 401);
                return response('Unauthorized', 401);
            }

            // Process the webhook
            $result = $this->processNowPaymentsWebhook($data);
            
            if ($result['success']) {
                $webhookCall->markProcessed($result['data']);
                return response('OK', 200);
            } else {
                $webhookCall->markFailed($result['error'], 500);
                return response('Processing failed', 500);
            }

        } catch (\Exception $e) {
            Log::error('Webhook processing exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'payload' => $payload ?? null
            ]);

            if (isset($webhookCall)) {
                $webhookCall->markFailed($e->getMessage(), 500);
            }

            return response('Internal error', 500);
        }
    }

    /**
     * Process NowPayments webhook data
     */
    private function processNowPaymentsWebhook(array $data): array
    {
        try {
            $orderId = $data['order_id'] ?? null;
            $paymentStatus = $data['payment_status'] ?? null;
            $paymentId = $data['payment_id'] ?? null;

            if (!$orderId) {
                return [
                    'success' => false,
                    'error' => 'Missing order_id in webhook data'
                ];
            }

            // Find the swap
            $swap = Swap::where('swap_id', $orderId)->first();
            if (!$swap) {
                return [
                    'success' => false,
                    'error' => "Swap not found: {$orderId}"
                ];
            }

            // Update swap with payment information
            $swap->update([
                'nowpayments_payment_id' => $paymentId,
                'payment_status' => $paymentStatus,
            ]);

            // Process based on payment status
            switch ($paymentStatus) {
                case Swap::PAYMENT_STATUS_WAITING:
                    $this->handlePaymentWaiting($swap, $data);
                    break;

                case Swap::PAYMENT_STATUS_CONFIRMING:
                    $this->handlePaymentConfirming($swap, $data);
                    break;

                case Swap::PAYMENT_STATUS_CONFIRMED:
                case Swap::PAYMENT_STATUS_FINISHED:
                    $this->handlePaymentConfirmed($swap, $data);
                    break;

                case Swap::PAYMENT_STATUS_PARTIALLY_PAID:
                    $this->handlePaymentPartiallyPaid($swap, $data);
                    break;

                case Swap::PAYMENT_STATUS_FAILED:
                    $this->handlePaymentFailed($swap, $data);
                    break;

                case Swap::PAYMENT_STATUS_EXPIRED:
                    $this->handlePaymentExpired($swap, $data);
                    break;

                case Swap::PAYMENT_STATUS_REFUNDED:
                    $this->handlePaymentRefunded($swap, $data);
                    break;

                default:
                    LoggingService::logError("Unknown payment status: {$paymentStatus}", $data, $orderId);
            }

            return [
                'success' => true,
                'data' => [
                    'swap_id' => $orderId,
                    'payment_status' => $paymentStatus,
                    'processed_at' => now()->toISOString()
                ]
            ];

        } catch (\Exception $e) {
            LoggingService::logError('Webhook processing exception', [
                'error' => $e->getMessage(),
                'data' => $data
            ], $data['order_id'] ?? null);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Handle payment waiting status
     */
    private function handlePaymentWaiting(Swap $swap, array $data): void
    {
        if ($swap->status === Swap::STATUS_PENDING) {
            $swap->update(['status' => Swap::STATUS_WAITING_PAYMENT]);
        }

        LoggingService::logPaymentReceived($swap->swap_id, $data);
    }

    /**
     * Handle payment confirming status
     */
    private function handlePaymentConfirming(Swap $swap, array $data): void
    {
        $swap->update([
            'status' => Swap::STATUS_PAYMENT_RECEIVED,
            'paid_at' => now()
        ]);

        LoggingService::logPaymentReceived($swap->swap_id, $data);
    }

    /**
     * Handle payment confirmed status
     */
    private function handlePaymentConfirmed(Swap $swap, array $data): void
    {
        // Check if swap is still waiting for payment (not already processed)
        if ($swap->status === Swap::STATUS_WAITING_PAYMENT) {
            $swap->update([
                'status' => Swap::STATUS_PROCESSING,
                'paid_at' => now()
            ]);

            LoggingService::logPaymentConfirmed($swap->swap_id, $data);

            // Process USDZ transfer
            $this->processUSDZTransfer($swap);
        } else {
            Log::info("Payment confirmed but swap already processed", [
                'swap_id' => $swap->swap_id,
                'current_status' => $swap->status,
                'payment_status' => $swap->payment_status
            ]);
        }
    }

    /**
     * Handle partially paid status
     */
    private function handlePaymentPartiallyPaid(Swap $swap, array $data): void
    {
        LoggingService::logError('Payment partially paid', $data, $swap->swap_id);
        
        $swap->update([
            'status' => Swap::STATUS_FAILED,
            'error_message' => 'Payment was only partially received'
        ]);
    }

    /**
     * Handle payment failed status
     */
    private function handlePaymentFailed(Swap $swap, array $data): void
    {
        $swap->update([
            'status' => Swap::STATUS_FAILED,
            'error_message' => 'Payment failed'
        ]);

        LoggingService::logSwapFailed($swap->swap_id, 'Payment failed', $data);
    }

    /**
     * Handle payment expired status
     */
    private function handlePaymentExpired(Swap $swap, array $data): void
    {
        $swap->update(['status' => Swap::STATUS_EXPIRED]);
        LoggingService::logSwapExpired($swap->swap_id);
    }

    /**
     * Handle payment refunded status
     */
    private function handlePaymentRefunded(Swap $swap, array $data): void
    {
        LoggingService::logError('Payment was refunded', $data, $swap->swap_id);
        
        $swap->update([
            'status' => Swap::STATUS_FAILED,
            'error_message' => 'Payment was refunded'
        ]);
    }

    /**
     * Process USDZ token transfer
     */
    private function processUSDZTransfer(Swap $swap): void
    {
        try {
            // Log USDZ transfer attempt
            SwapLog::log(
                $swap->swap_id,
                SwapLog::EVENT_USDZ_SEND_ATTEMPT,
                "Starting USDZ transfer after payment confirmation",
                [
                    'to_address' => $swap->zhcash_address,
                    'amount' => $swap->output_amount,
                    'payment_confirmed' => true
                ]
            );

            // Use ZHNodeService for USDZ transfer
            $result = $this->zhnode->sendUSDZ(
                $swap->zhcash_address,
                $swap->output_amount,
                $swap->swap_id
            );

            if ($result['success']) {
                $txid = $result['data']['txid'] ?? $result['txid'] ?? null;
                $amount = $result['data']['amount'] ?? $result['amount'] ?? $swap->output_amount;

                $swap->update([
                    'status' => Swap::STATUS_COMPLETED,
                    'zhcash_txid' => $txid,
                    'zhcash_sent_amount' => $amount,
                    'usdz_sent' => true,
                    'usdz_txid' => $txid,
                    'completed_at' => now()
                ]);

                SwapLog::log(
                    $swap->swap_id,
                    SwapLog::EVENT_USDZ_SENT,
                    "USDZ tokens successfully sent",
                    [
                        'txid' => $txid,
                        'amount' => $amount,
                        'to_address' => $swap->zhcash_address
                    ]
                );

                SwapLog::log(
                    $swap->swap_id,
                    SwapLog::EVENT_SWAP_COMPLETED,
                    "Swap completed successfully",
                    [
                        'zhcash_txid' => $txid,
                        'amount_sent' => $amount,
                        'completed_at' => now()->toISOString()
                    ]
                );

                LoggingService::logSwapCompleted($swap->swap_id, [
                    'zhcash_txid' => $txid,
                    'amount_sent' => $amount
                ]);
            } else {
                $swap->update([
                    'status' => Swap::STATUS_FAILED,
                    'error_message' => 'Failed to send USDZ tokens: ' . $result['error']
                ]);

                SwapLog::log(
                    $swap->swap_id,
                    SwapLog::EVENT_SWAP_FAILED,
                    "USDZ transfer failed: " . $result['error'],
                    [
                        'error' => $result['error'],
                        'to_address' => $swap->zhcash_address,
                        'amount' => $swap->output_amount
                    ],
                    SwapLog::LEVEL_ERROR
                );

                LoggingService::logSwapFailed($swap->swap_id, 'USDZ transfer failed', [
                    'error' => $result['error']
                ]);
            }
        } catch (\Exception $e) {
            $swap->update([
                'status' => Swap::STATUS_FAILED,
                'error_message' => 'Exception during USDZ transfer: ' . $e->getMessage()
            ]);

            SwapLog::log(
                $swap->swap_id,
                SwapLog::EVENT_SWAP_FAILED,
                "USDZ transfer exception: " . $e->getMessage(),
                [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'to_address' => $swap->zhcash_address,
                    'amount' => $swap->output_amount
                ],
                SwapLog::LEVEL_ERROR
            );

            LoggingService::logSwapFailed($swap->swap_id, 'USDZ transfer exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Get event type from webhook data
     */
    private function getEventType(array $data): string
    {
        $status = $data['payment_status'] ?? 'unknown';
        return "payment.{$status}";
    }
}
