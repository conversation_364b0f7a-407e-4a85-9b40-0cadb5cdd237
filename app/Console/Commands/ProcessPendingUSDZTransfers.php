<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Swap;
use App\Services\ZHCashService;
use App\Models\SwapLog;

class ProcessPendingUSDZTransfers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'usdz:process-pending {--limit=10 : Maximum number of transfers to process}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process pending USDZ transfers for swaps with payment_received status';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $limit = (int) $this->option('limit');

        $this->info("🔍 Processing pending USDZ transfers (limit: {$limit})...");

        // Find swaps with payment_received status that need USDZ transfer
        $swaps = Swap::where('status', Swap::STATUS_PAYMENT_RECEIVED)
            ->whereNull('zhcash_txid')
            ->limit($limit)
            ->get();

        if ($swaps->isEmpty()) {
            $this->info('✅ No pending USDZ transfers found.');
            return;
        }

        $this->info("📦 Found {$swaps->count()} swap(s) requiring USDZ transfer.");

        foreach ($swaps as $swap) {
            $this->processUSDZTransfer($swap);
        }

        $this->info('✅ Processing completed.');
    }

    /**
     * Process USDZ transfer for a swap
     */
    private function processUSDZTransfer(Swap $swap): void
    {
        $this->info("🔄 Processing USDZ transfer for swap: {$swap->swap_id}");

        try {
            // Update status to processing
            $swap->update(['status' => Swap::STATUS_PROCESSING]);

            SwapLog::log(
                $swap->swap_id,
                SwapLog::EVENT_STATUS_UPDATE,
                "Processing USDZ transfer via manual command",
                ['previous_status' => Swap::STATUS_PAYMENT_RECEIVED]
            );

            // Send USDZ tokens
            $zhcashService = new ZHCashService();
            $result = $zhcashService->sendUSDZ(
                $swap->zhcash_address,
                $swap->output_amount,
                $swap->swap_id
            );

            if ($result['success']) {
                $swap->update([
                    'status' => Swap::STATUS_COMPLETED,
                    'zhcash_txid' => $result['data']['txid'],
                    'zhcash_sent_amount' => $result['data']['amount'],
                    'completed_at' => now()
                ]);

                SwapLog::log(
                    $swap->swap_id,
                    SwapLog::EVENT_SWAP_COMPLETED,
                    "Swap completed via manual USDZ processing",
                    [
                        'txid' => $result['data']['txid'],
                        'amount' => $result['data']['amount'],
                        'source' => 'manual_usdz_command'
                    ]
                );

                $this->info("✅ USDZ transfer completed for {$swap->swap_id}");
                $this->info("   TXID: {$result['data']['txid']}");
                $this->info("   Amount: {$result['data']['amount']} USDZ");
            } else {
                $swap->update([
                    'status' => Swap::STATUS_FAILED,
                    'error_message' => $result['error'] ?? 'USDZ transfer failed'
                ]);

                SwapLog::log(
                    $swap->swap_id,
                    SwapLog::EVENT_SWAP_FAILED,
                    "USDZ transfer failed via manual command: " . ($result['error'] ?? 'Unknown error'),
                    ['result' => $result]
                );

                $this->error("❌ USDZ transfer failed for {$swap->swap_id}: " . ($result['error'] ?? 'Unknown error'));
            }

        } catch (\Exception $e) {
            $swap->update([
                'status' => Swap::STATUS_FAILED,
                'error_message' => "USDZ transfer exception: " . $e->getMessage()
            ]);

            SwapLog::log(
                $swap->swap_id,
                SwapLog::EVENT_SWAP_FAILED,
                "USDZ transfer exception via manual command: " . $e->getMessage(),
                ['exception' => $e->getTraceAsString()]
            );

            $this->error("❌ Exception during USDZ transfer for {$swap->swap_id}: " . $e->getMessage());
        }
    }
}
